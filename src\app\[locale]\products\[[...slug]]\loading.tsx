"use client"

import { useEffect } from "react";
import "./loading.scss";

const loadingArray = new Array(10).fill(0);

export default function Loading() {

  useEffect(() => {
    if (typeof window !== "undefined") window.scroll({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
  }, [])

  return (
    <div className="container">
      <div className="loading-section">
        {loadingArray.map((_, index) => (
          <div key={index} className="loading_card skeletonLoader"></div>
        ))}
      </div>
    </div>
  );
}
