import type { <PERSON><PERSON><PERSON>, Viewport } from "next";
import "./globals.scss";
import "../../style.css"
import "../../styles/style.scss";
import Footer from "@/components/footer/Footer";
import MobileMenu from "@/components/mobile-menu/MobileMenu";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import MainLoader from "@/components/main-loader/MainLoader";
import { Toaster } from "sonner";
import HeaderSection from "@/components/header/HeaderSection";
import { HistoryProvider } from "@/contexts/HistoryProvider";
import SplashScreen from "@/components/splash-screen/SplashScreen";
// import { I18nProviderClient } from "@/locales/client";

import { Suspense } from "react";
import { TranslationProvider } from "@/contexts/Translation";
import { endpoints } from "@/config/apiEndpoints";
import { getTranslation } from "@/lib/methods/translation";
import { AuthProvider } from "@/contexts/AuthProvider";
import { SettingsProvider } from "@/contexts/SettingsProvider";
import { createI18nClient } from "next-international/client";
import { LocaleProvider } from "@/contexts/LocaleProvider";
import { getMultiStores } from "@/lib/methods/multistore";

import "swiper/css";
import "swiper/css/free-mode";
import "swiper/css/thumbs";
import "swiper/css/effect-fade";
import "swiper/css/pagination";
import "swiper/css/navigation";
import Script from "next/script";

const TAMARA_PUBLIC_KEY = process.env.NEXT_PUBLIC_TAMARA_PUBLIC_KEY ?? "";

export async function generateMetadata({
  params,
}: {
  params: any;
}): Promise<Metadata> {
  const locale = params?.locale || "ae-en";
  const [storeId, language] = locale.split("-")
  const res = await fetch(process.env.NEXT_PUBLIC_API_URL + endpoints.header, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      language: language || "en",
      storeid: storeId || "ae"
    },
    next: { tags: ["header"] },
    cache: "force-cache"
  });

  const data = await res.json();

  return {
    title: locale.includes("ar") ? data?.result?.seoDetails?.title?.ar : data?.result?.seoDetails?.title?.en || "Yateem Optician",
    description: locale.includes("ar") ? data?.result?.seoDetails?.description?.ar : data?.result?.seoDetails?.description?.en || "Yateem Optician",
    keywords: locale.includes("ar") ? data?.result?.seoDetails?.keywords?.ar : data?.result?.seoDetails?.keywords?.en || "Contact Lens  | Yateem Opticians",

    openGraph: {
      title: locale.includes("ar") ? data?.result?.seoDetails?.title?.ar : data?.result?.seoDetails?.title?.en || "Contact Lens  | Yateem Opticians",
      description: locale.includes("ar") ? data?.result?.seoDetails?.description?.ar : data?.result?.seoDetails?.description?.en || "Yateem Optician",
      type: "website",
      images: [
        {
          url: data?.result?.seoDetails?.ogImage,
          width: 1200,
          height: 630,
        },
      ],
    },
    alternates: {
      canonical: locale.includes("ar") ? data?.result?.seoDetails?.canonical?.ar : data?.result?.seoDetails?.canonical?.en,
    },
  };
}

// export const metadata: Metadata = {
//   title: "Yateem Optician",
//   description: "Yateem Optician | Shop for the best contact lenses, spectacles and more",
//   keywords:
//     "yateem optician, yateem, optician, contact lenses, spectacles, glasses, eyeglasses, sunglasses, prescription lenses, prescription contact lenses, prescription spectacles, prescription glasses, prescription eyeglasses, prescription sunglasses",
//   metadataBase: new URL("https://yateem.com"),
//   alternates: {
//     languages: {
//       en: "/en",
//       ar: "/ar",
//     },
//   },
//   openGraph: {
//     images: ["/opengraph-image.png"],
//   },
//   verification: { google: "EGuVZ8w6tQ_or029KIMR_3uuPbdW1VaPVHxyCwdSeHs" }
// };


export const viewport: Viewport = {
  themeColor: "black",

};

export async function getHeaderData(locale: string) {
  // const locale = cookies().get("Next-Locale")?.value || "ae-en";
  const [storeId, language] = locale.split("-")
  const res = await fetch(process.env.NEXT_PUBLIC_API_URL + endpoints.header, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      language: language || "en",
      storeid: storeId || "ae"
    },
    next: { tags: ["header"] },
    cache: "force-cache"
  });

  const data = await res.json();

  if (data?.errorCode !== 0) {
    throw Error("Something went wrong");
  }
  return data
}


export default async function RootLayout(props: {
  children: React.ReactNode;
  auth: React.ReactNode;
  test: React.ReactNode;
  params: { locale: string };
}) {
  // await new Promise((resolve) => setTimeout(resolve, 10000));
  const [data, headerData, storesData] = await Promise.all([getTranslation(props.params.locale), getHeaderData(props.params.locale), getMultiStores()])
  const locales: string[] = [];
  const countryCodes: any = {};
  let storeName = "";
  let currencyCode: any = "AED";
  storesData?.result?.forEach((item: any) => {
    locales.push(`${item?.storeId}-en`)
    locales.push(`${item?.storeId}-ar`)
    countryCodes[item?.storeId] = item?.countryCode
    if (props.params.locale.includes(item?.storeId)) {
      currencyCode = item?.currencyCode
      storeName = item?.name
    }
  })

  return (
    <div className={`app ${props.params.locale.includes("ar") ? "rtl" : ""}`}>
      <TranslationProvider data={data.result}>
        <LocaleProvider locales={locales} countryCodes={countryCodes} locale={props.params?.locale} currencyCode={currencyCode}>
          <SettingsProvider data={headerData?.result} store={storeName}>
            <HistoryProvider>
              <SplashScreen />
              <HeaderSection stores={storesData?.result} data={headerData} />
              <AuthProvider>
                <MobileMenu translation={data.result} />
              </AuthProvider>
              <ReactQueryDevtools initialIsOpen={false} />
              <Suspense fallback={<MainLoader />}>{props.auth}</Suspense>
              <AuthProvider>
                <Suspense fallback={<MainLoader />}>{props.children}</Suspense>
              </AuthProvider>
              <Suspense fallback={<MainLoader />}>
                <Toaster
                  position="top-center"
                  theme="dark"
                  offset={"100px"}
                  duration={3000}
                  toastOptions={{
                    style: { justifyContent: "center", borderRadius: "30px" },
                  }}
                />
              </Suspense>
              <Suspense fallback={<>Loading...</>}>
                <Footer translation={data.result} locale={props.params.locale} />
              </Suspense>
            </HistoryProvider>
          </SettingsProvider>
        </LocaleProvider>
      </TranslationProvider>
    </div>
  );
}
