# Yateem Optician Frontend - Complete Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture & Technology Stack](#architecture--technology-stack)
3. [Getting Started](#getting-started)
4. [Project Structure](#project-structure)
5. [Core Features](#core-features)
6. [Internationalization & Multi-Store Support](#internationalization--multi-store-support)
7. [Authentication & User Management](#authentication--user-management)
8. [E-commerce Features](#e-commerce-features)
9. [Advanced Features](#advanced-features)
10. [API Integration](#api-integration)
11. [Styling & UI Components](#styling--ui-components)
12. [State Management](#state-management)
13. [Performance & SEO](#performance--seo)
14. [Deployment](#deployment)
15. [Development Guidelines](#development-guidelines)
16. [Troubleshooting](#troubleshooting)

## Project Overview

### Business Context
**Yateem Optician** is a comprehensive e-commerce platform for optical products serving multiple Middle Eastern markets. The platform specializes in:
- **Eyeglasses & Sunglasses**: Wide selection of prescription and non-prescription eyewear
- **Contact Lenses**: Daily, weekly, monthly lenses with subscription services
- **Prescription Services**: Upload, manage, and fulfill prescription orders
- **Virtual Try-On**: Advanced AR technology for trying glasses virtually
- **Insurance Integration**: Support for various insurance providers
- **Multi-Store Operations**: Serving UAE, Saudi Arabia, Qatar, Oman, and Bahrain

### Technical Overview
- **Name**: Yateem Optician Frontend
- **Version**: 0.1.0
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: SCSS with Bootstrap integration
- **Target Users**: End customers, optician store staff, administrators
- **Supported Markets**: UAE, Saudi Arabia, Qatar, Oman, Bahrain
- **Languages**: English and Arabic (RTL support)

## Architecture & Technology Stack

### Frontend Framework & Core Technologies
- **Next.js 14**: App Router with Server Components and Client Components
- **React 18**: Latest React features including Suspense and Concurrent Features
- **TypeScript**: Full type safety across the application
- **Node.js**: Runtime environment for server-side operations

### Styling & UI
- **SCSS**: Advanced CSS preprocessing with variables and mixins
- **Bootstrap 5**: Responsive grid system and utility classes
- **React Bootstrap**: Bootstrap components for React
- **Custom Components**: Extensive library of reusable UI components
- **RTL Support**: Right-to-left layout for Arabic language
- **Responsive Design**: Mobile-first approach with breakpoint management

### State Management & Data Fetching
- **TanStack Query (React Query)**: Server state management with caching
- **React Context API**: Global state for authentication, locale, and settings
- **React Hook Form**: Efficient form state management with validation
- **Custom Hooks**: Reusable logic for common operations

### Internationalization & Localization
- **next-international**: Multi-language support with dynamic imports
- **Multi-Store Support**: Country-specific configurations (UAE, SA, QA, OM, BH)
- **Currency Support**: Multiple currencies (AED, SAR, QAR, OMR, BHD)
- **RTL Layout**: Complete Arabic language support

### Third-Party Integrations
- **Luxottica Virtual Mirror**: AR-powered virtual try-on technology
- **Google Maps API**: Store locator and address autocomplete
- **Firebase**: Authentication and real-time features
- **Payment Gateways**: Tamara (buy now, pay later), Tabby
- **Analytics**: Google Analytics 4, Google Tag Manager, Microsoft Clarity
- **Social Login**: Google OAuth integration

### Development & Build Tools
- **ESLint**: Code linting with Next.js configuration
- **Sharp**: High-performance image processing
- **Sass**: CSS preprocessing
- **TypeScript**: Static type checking
- **Axios**: HTTP client with interceptors for API communication

## Getting Started

### Prerequisites
- **Node.js**: Version 18.0 or higher
- **npm/yarn/pnpm**: Package manager
- **Git**: Version control

### Installation
```bash
# Clone the repository
git clone <repository-url>

# Navigate to the project directory
cd yateem-optician-frontend

# Install dependencies
npm install
# or
yarn install
# or
pnpm install
```

### Environment Configuration
Create a `.env.local` file in the root directory:

```env
# API Configuration
NEXT_PUBLIC_API_URL=https://yateemapi.s423.previewbay.com/api/web/
NEXT_PUBLIC_IMAGE_DOMAIN=yateemapi.s423.previewbay.com

# Analytics
GAID=G-XXXXXXXXXX
GTID=GTM-XXXXXXX

# Payment Integration
TAMARA_PUBLIC_KEY=your_tamara_public_key

# Virtual Mirror
LUXOTTICA_VM_KEY=9d892082-2c7d-4c68-8503-660f15f05c3b

# Google Maps
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Firebase (if using)
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
FIREBASE_PROJECT_ID=your_project_id

# Application Settings
PAGESIZE=15
```

### Development Scripts
```bash
# Start development server
npm run dev          # Runs on http://localhost:3000
npm run lux          # Runs on http://localhost:1347

# Build for production
npm run build

# Start production server
npm run start        # Runs on port 1347

# Linting
npm run lint
```

### Development Server Access
- **Default**: [http://localhost:3000](http://localhost:3000)
- **Custom Port**: [http://localhost:1347](http://localhost:1347) (using `npm run lux`)

## Project Structure

```
yateem-optician-frontend/
├── public/                          # Static assets
│   ├── icons/                       # Icon files
│   ├── images/                      # Image assets
│   ├── videos/                      # Video files
│   ├── pwa/                         # PWA icons and manifest
│   └── logo.svg                     # Brand assets
├── src/
│   ├── app/                         # Next.js App Router
│   │   ├── [locale]/               # Internationalized routes
│   │   │   ├── layout.tsx          # Main layout with providers
│   │   │   ├── page.tsx            # Homepage
│   │   │   ├── products/           # Product listing pages
│   │   │   ├── product/[slug]/     # Product detail pages
│   │   │   ├── cart/               # Shopping cart
│   │   │   ├── checkout/           # Checkout process
│   │   │   ├── my-accounts/        # User account pages
│   │   │   ├── store-locator/      # Store finder
│   │   │   ├── contact-lens/       # Contact lens subscription
│   │   │   ├── insurance/          # Insurance integration
│   │   │   └── ...                 # Other pages
│   │   ├── @auth/                  # Parallel route for auth
│   │   ├── api/                    # API routes
│   │   ├── layout.tsx              # Root layout
│   │   ├── manifest.ts             # PWA manifest
│   │   └── sitemap.ts              # SEO sitemap
│   ├── components/                  # Reusable UI components
│   │   ├── header/                 # Header components
│   │   ├── footer/                 # Footer components
│   │   ├── home/                   # Homepage sections
│   │   ├── product/                # Product-related components
│   │   ├── product-detail/         # Product detail components
│   │   ├── product-listing/        # Product listing components
│   │   ├── cart/                   # Cart components
│   │   ├── checkout/               # Checkout components
│   │   ├── my-accounts/            # Account management
│   │   ├── auth/                   # Authentication components
│   │   ├── common/                 # Shared components
│   │   └── ...                     # Feature-specific components
│   ├── contexts/                   # React Context providers
│   │   ├── AuthProvider.tsx        # Authentication state
│   │   ├── LocaleProvider.tsx      # Internationalization
│   │   ├── SettingsProvider.tsx    # App settings
│   │   ├── TranslationProvider.tsx # Translation data
│   │   └── HistoryProvider.tsx     # Navigation history
│   ├── lib/                        # Utility functions and API methods
│   │   ├── methods/                # API call functions
│   │   │   ├── auth.ts             # Authentication APIs
│   │   │   ├── cart.ts             # Cart operations
│   │   │   ├── products.ts         # Product APIs
│   │   │   ├── insurance.ts        # Insurance APIs
│   │   │   └── ...                 # Other API methods
│   │   └── utils/                  # Helper functions
│   ├── config/                     # Configuration files
│   │   ├── apiEndpoints.ts         # API endpoint definitions
│   │   ├── axios.interceptor.ts    # HTTP client configuration
│   │   └── firebase.config.ts      # Firebase configuration
│   ├── locales/                    # Translation files
│   │   ├── en.ts                   # English translations
│   │   └── ar.ts                   # Arabic translations
│   ├── styles/                     # Global styles
│   │   ├── style.scss              # Main stylesheet
│   │   ├── vendors/                # Third-party styles
│   │   └── components/             # Component-specific styles
│   ├── hooks/                      # Custom React hooks
│   ├── fonts/                      # Custom font files
│   ├── middleware.ts               # Next.js middleware
│   └── types.d.ts                  # TypeScript type definitions
├── package.json                    # Dependencies and scripts
├── tsconfig.json                   # TypeScript configuration
├── next.config.js                  # Next.js configuration
├── .eslintrc.json                  # ESLint configuration
└── README.md                       # Basic project information
```

## Core Features

### E-commerce Functionality
- **Product Catalog**: Browse eyeglasses, sunglasses, and contact lenses
- **Advanced Filtering**: Filter by brand, price, color, shape, material, etc.
- **Product Search**: Intelligent search with suggestions
- **Product Comparison**: Compare multiple products side-by-side
- **Wishlist**: Save products for later purchase
- **Shopping Cart**: Add, remove, and modify cart items
- **Checkout Process**: Multi-step checkout with address and payment
- **Order Management**: Track orders, view history, cancel orders

### Virtual Try-On Technology
- **Luxottica Virtual Mirror**: AR-powered virtual try-on for eyeglasses
- **Real-time Rendering**: Live camera feed with 3D product overlay
- **UPC Validation**: Check product availability for virtual try-on
- **Terms & Conditions**: Privacy policy integration for camera usage
    ```ts
    import axios from 'axios';
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/products`);
    ```
- **Authentication:**
  - Managed via `AuthProvider` context and cookies for session persistence.
  - Login, logout, and session validation handled centrally.
- **State Management:**
  - Context API for auth and locale; React Query for server state.
- **SEO and Metadata:**
  - Uses `next/head` and `next/metadata` for dynamic SEO tags.
- **Responsive Design:**
  - SCSS and Bootstrap for layouts; custom styles for branding.

### Contact Lens Subscription Service
- **Subscription Plans**: Monthly, quarterly, and annual plans
- **Auto-delivery**: Automatic recurring orders
- **Prescription Management**: Upload and manage prescriptions
- **Power Selection**: Comprehensive power options for different lens types
- **Subscription Management**: Pause, modify, or cancel subscriptions

### Try Cart System
- **Home Try-On**: Order frames to try at home before purchasing
- **Try Cart Management**: Add/remove items from try cart
- **Booking System**: Schedule try-on appointments
- **Return Process**: Easy return system for tried products

### Insurance Integration
- **Provider Support**: Integration with multiple insurance providers
- **Claim Processing**: Submit insurance claims directly through the platform
- **Coverage Verification**: Check coverage for specific products
- **Form Submission**: Digital insurance forms with file upload

### Store Locator
- **Google Maps Integration**: Interactive map with store locations
- **Location Search**: Search by city, country, or current location
- **Store Details**: Contact information, hours, and services
- **Directions**: Get directions to nearest store

### Prescription Services
- **Upload System**: Upload prescription images or PDFs
- **Prescription Management**: View, edit, and delete prescriptions
- **Validation**: Prescription format validation
- **Integration**: Link prescriptions to orders

### Loyalty Program
- **Points System**: Earn points on purchases
- **Redemption**: Redeem points for discounts
- **Cashback**: Track cashback earnings
- **Tier System**: Different loyalty tiers with benefits

## Internationalization & Multi-Store Support

### Supported Markets & Locales
The application supports multiple Middle Eastern markets with localized content:

| Country | Store ID | Locales | Currency | Country Code |
|---------|----------|---------|----------|--------------|
| UAE | ae | ae-en, ae-ar | AED | +971 |
| Saudi Arabia | sa | sa-en, sa-ar | SAR | +966 |
| Qatar | qa | qa-en, qa-ar | QAR | +974 |
| Oman | om | om-en, om-ar | OMR | +968 |
| Bahrain | bh | bh-en, bh-ar | BHD | +973 |

### Locale Management
```typescript
// Locale context usage
const { currentLocale, changeLocale, t, currencyCode } = useLocaleContext();

// Change locale
changeLocale('sa-ar'); // Switch to Saudi Arabia Arabic

// Get translations
const welcomeText = t('welcome'); // Returns localized text

// Currency formatting
const price = formatCurrency(100, currencyCode); // "100 SAR"
```

### RTL (Right-to-Left) Support
- **Automatic Detection**: RTL layout applied for Arabic locales
- **CSS Classes**: `.rtl` class added to body for Arabic content
- **Component Adaptation**: All components support RTL layout
- **Icon Mirroring**: Directional icons automatically mirrored

### Translation System
- **Dynamic Loading**: Translations loaded on-demand per locale
- **Fallback**: English as fallback for missing translations
- **Server-side**: Translations fetched server-side for SEO
- **Client-side**: Dynamic translation switching without page reload

## Authentication & User Management

### Authentication Flow
1. **Phone Number Entry**: User enters phone number with country code
2. **OTP Verification**: SMS OTP sent and verified
3. **User Type Selection**: Choose between individual or business account
4. **Profile Completion**: Complete user profile information
5. **Session Management**: JWT token stored in secure cookies

### Authentication Components
```typescript
// AuthProvider context
const { isLoggedIn, user, userProfile } = useContext(AuthContext);

// Login process
import { login } from '@/lib/methods/auth';
await login(phoneNumber, otp);

// Logout
import { logout } from '@/lib/methods/auth';
logout(translation);
```

### Protected Routes
- **Middleware Protection**: `/my-accounts/*` routes require authentication
- **Redirect Logic**: Unauthenticated users redirected to login
- **Guest Access**: Some features available for guest users
- **Session Validation**: Automatic token validation and refresh

### User Account Features
- **Profile Management**: Edit personal information
- **Address Book**: Manage multiple delivery addresses
- **Order History**: View past orders and track current ones
- **Prescription Management**: Upload and manage prescriptions
- **Wishlist**: Save favorite products
- **Loyalty Points**: Track and redeem loyalty points
- **Subscription Management**: Manage contact lens subscriptions

## E-commerce Features

### Product Catalog
- **Product Types**: Eyeglasses, sunglasses, contact lenses
- **Product Variants**: Different colors, sizes, and materials
- **Brand Management**: Support for multiple eyewear brands
- **Category System**: Hierarchical product categorization
- **Stock Management**: Real-time stock tracking
- **Pricing**: Support for regular and offer prices

### Shopping Cart
```typescript
// Add to cart
import { addToCart } from '@/lib/methods/cart';
await addToCart(productId, quantity, sizeId, lensId);

// Update cart
import { updateCart } from '@/lib/methods/cart';
await updateCart(productId, newQuantity, sizeId, lensId);

// Remove from cart
import { removeFromCart } from '@/lib/methods/cart';
await removeFromCart(productId, sizeId, lensId);
```

### Checkout Process
1. **Cart Review**: Review items, quantities, and prices
2. **Address Selection**: Choose or add delivery address
3. **Shipping Options**: Select delivery method and timing
4. **Payment Method**: Choose payment gateway (Tamara, Tabby, etc.)
5. **Order Confirmation**: Final review and order placement
6. **Payment Processing**: Secure payment processing
7. **Order Tracking**: Real-time order status updates

### Order Management
- **Order History**: View all past orders
- **Order Details**: Detailed order information
- **Order Tracking**: Real-time status updates
- **Order Cancellation**: Cancel orders within allowed timeframe
- **Returns & Exchanges**: Process returns and exchanges
- **Invoice Generation**: PDF invoice generation

### Coupon & Discount System
- **Coupon Codes**: Apply discount coupons at checkout
- **Automatic Discounts**: System-applied discounts
- **Loyalty Discounts**: Points-based discounts
- **Bulk Discounts**: Quantity-based pricing
- **Seasonal Offers**: Time-limited promotional offers

## Advanced Features

### Virtual Try-On Integration
```typescript
// Virtual Mirror implementation
import { VirtualMirror, VirtualMirrorCatalogue } from '@luxottica/virtual-mirror';

// Initialize virtual mirror
const initOptions = {
  key: 'your-luxottica-key',
  brand: 'yateem',
  locale: 'en-US'
};

// Check product availability
const isAvailable = await VMCatalogue.isUpcSupported(productUPC);

// Render virtual mirror
await VirtualMirror.renderMirror({
  target: 'mirror-container',
  upc: productUPC
});
```

### Lens Customization
- **Lens Types**: Single vision, progressive, bifocal
- **Lens Materials**: Different material options
- **Lens Coatings**: Anti-glare, blue light, photochromic
- **Lens Index**: Various refractive indices
- **Prescription Integration**: Link prescriptions to lens orders

### Payment Integration
```typescript
// Tamara integration
window.tamaraWidgetConfig = {
  lang: locale.split("-")[1],
  country: "AE",
  publicKey: TAMARA_PUBLIC_KEY
};

// Tabby integration
<Script src="https://checkout.tabby.ai/tabby-promo.js" />
```

### Analytics & Tracking
- **Google Analytics 4**: Enhanced e-commerce tracking
- **Google Tag Manager**: Event and conversion tracking
- **Microsoft Clarity**: User behavior analytics
- **Custom Events**: Product views, cart actions, purchases

### PWA Features
- **Service Worker**: Offline functionality
- **App Manifest**: Install as mobile app
- **Push Notifications**: Order updates and promotions
- **Offline Support**: Basic functionality without internet

## API Integration

### API Architecture
- **Base URL**: Configurable API endpoint
- **Authentication**: JWT token-based authentication
- **Interceptors**: Automatic token attachment and error handling
- **Error Handling**: Centralized error management with toast notifications
- **Caching**: React Query for intelligent data caching

### API Configuration
```typescript
// Axios interceptor setup
import api from '@/config/axios.interceptor';
import { endpoints } from '@/config/apiEndpoints';

// Example API call
const response = await api.post(endpoints.products, {
  page: 1,
  limit: 15,
  filters: { brand: 'rayban' }
});
```

### Key API Endpoints
```typescript
export const endpoints = {
  // Authentication
  login: "login",
  verifyOtp: "verify-otp",
  profile: "profile",

  // Products
  products: "products",
  productDetail: "product-detail",
  brands: "brands",
  categories: "categories",

  // Cart & Orders
  cart: "cart",
  addToCart: "add-to-cart",
  removeFromCart: "remove-from-cart",
  checkout: "checkout",
  orders: "orders",

  // User Management
  address: "address",
  addAddress: "add-address",
  wishlist: "wishlist",

  // Specialized Features
  virtualMirror: "virtual-mirror",
  uploadPrescription: "upload-prescription",
  insuranceForm: "insurance-form",
  storeLocator: "store-locator"
};
```

### Error Handling
- **HTTP Errors**: Automatic retry for network failures
- **Authentication Errors**: Automatic redirect to login
- **Validation Errors**: Form-specific error display
- **Toast Notifications**: User-friendly error messages
- **Fallback UI**: Error boundaries for component failures
  - Pagination, filtering, and search are supported on many endpoints (see API docs or code comments).

### API Request/Response Examples

**Login**
- Request:
  ```json
  POST /login
  {
    "email": "<EMAIL>",
    "password": "your_password"
  }
  ```
- Response:
  ```json
  {
    "errorCode": 0,
    "result": {
      "token": "jwt_token_string",
      "user": { "id": 123, "name": "John Doe" }
    }
  }
  ```

**Fetch Products**
- Request:
  ```json
  POST /products
  {
    "page": 1,
    "limit": 15,
    "keyword": "glasses"
  }
  ```
- Response:
  ```json
  {
    "errorCode": 0,
    "result": [
      { "id": 1, "name": "Ray-Ban RX1234", "price": 120 },
      { "id": 2, "name": "Oakley OX5678", "price": 150 }
    ]
  }
  ```

**Add to Cart**
- Request:
  ```json
  POST /add-to-cart
  {
    "productId": 1,
    "quantity": 2
  }
  ```
- Response:
  ```json
  {
    "errorCode": 0,
    "result": { "cartId": "abc123", "items": [ ... ] }
  }
  ```

**Error Example**
- Response:
  ```json
  {
    "errorCode": 401,
    "message": "Unauthorized. Please log in."
  }
  ```

## Styling & UI Components

### SCSS Architecture
```scss
// Main stylesheet structure
src/styles/
├── style.scss              // Main entry point
├── vendors/
│   ├── _variables.scss     // Bootstrap variable overrides
│   ├── _bootstrap.scss     // Bootstrap imports
│   └── _mixins.scss        // Custom mixins
├── components/             // Component-specific styles
├── pages/                  // Page-specific styles
└── utilities/              // Utility classes
```

### Design System
- **Color Palette**: Consistent brand colors defined in SCSS variables
- **Typography**: Custom font stack with fallbacks
- **Spacing**: Standardized spacing scale
- **Breakpoints**: Mobile-first responsive breakpoints
- **Components**: Reusable UI component library

### Bootstrap Integration
```scss
// Custom Bootstrap configuration
$primary: #14252c;          // Brand primary color
$secondary: #6c757d;        // Secondary color
$success: #28a745;          // Success state
$danger: #dc3545;           // Error state
$warning: #ffc107;          // Warning state

// Grid system
$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);
```

### Component Library
- **Header Components**: Navigation, search, user menu
- **Product Components**: Product cards, galleries, filters
- **Form Components**: Input fields, selectors, validation
- **Modal Components**: Popups, confirmations, forms
- **Layout Components**: Containers, grids, sections

### RTL Styling
```scss
// RTL-specific styles
.rtl {
  direction: rtl;
  text-align: right;

  .navbar-nav {
    margin-left: 0;
    margin-right: auto;
  }

  .dropdown-menu {
    left: auto;
    right: 0;
  }
}
```

## State Management

### Context Providers
```typescript
// Authentication context
export const AuthContext = createContext({
  isLoggedIn: false,
  user: null,
  userProfile: null
});

// Locale context
export const LocaleContext = createContext({
  currentLocale: 'ae-en',
  changeLocale: () => {},
  t: () => {},
  currencyCode: 'AED'
});

// Settings context
export const SettingsContext = createContext({
  settings: {},
  store: ''
});
```

### React Query Configuration
```typescript
// Query client setup
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,    // 5 minutes
      cacheTime: 10 * 60 * 1000,   // 10 minutes
      retry: 3,
      refetchOnWindowFocus: false
    }
  }
});

// Usage example
const { data, isLoading, error } = useQuery({
  queryKey: ['products', filters],
  queryFn: () => getProducts(filters),
  enabled: !!filters
});
```

### Form State Management
```typescript
// React Hook Form usage
import { useForm } from 'react-hook-form';

const { register, handleSubmit, formState: { errors } } = useForm({
  defaultValues: {
    email: '',
    password: ''
  }
});

const onSubmit = (data) => {
  // Handle form submission
};
```

## Internationalization (i18n)
- Uses `next-intl` or similar for locale management.
- Language files in `src/locales/`.
- To add a language: add a new JSON file, update provider, and add a language option to the UI.

## Styling & Theming
- SCSS modules for component-level styles.
- Bootstrap for grid/layout and base components.
- Theming can be customized via SCSS variables in `src/styles/`.

## Development Guidelines

### Code Standards
- **TypeScript**: Use strict TypeScript configuration
- **ESLint**: Follow Next.js ESLint configuration
- **Prettier**: Consistent code formatting
- **Component Structure**: Functional components with hooks
- **File Naming**: PascalCase for components, camelCase for utilities

### Component Development
```typescript
// Component template
import React from 'react';
import styles from './ComponentName.module.scss';

interface ComponentNameProps {
  title: string;
  children?: React.ReactNode;
}

export const ComponentName: React.FC<ComponentNameProps> = ({
  title,
  children
}) => {
  return (
    <div className={styles.container}>
      <h2>{title}</h2>
      {children}
    </div>
  );
};

export default ComponentName;
```

### API Method Development
```typescript
// API method template
import { endpoints } from '@/config/apiEndpoints';
import api from '@/config/axios.interceptor';

export const getResourceData = async (params: any) => {
  try {
    const response = await api.post(endpoints.resource, params);
    if (response.data.errorCode === 0) {
      return response.data.result;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('Error fetching resource:', error);
    throw error;
  }
};
```

### Git Workflow
```bash
# Feature development
git checkout -b feature/feature-name
git add .
git commit -m "feat: add new feature --to build"
git push origin feature/feature-name

# Create pull request for review
# After approval, merge to main branch
```

### Deployment Trigger
Add `--to build` to commit messages to trigger deployment:
```bash
git commit -m "fix: resolve cart issue --to build"
```

## Performance & SEO

### Performance Optimizations
- **Next.js Image**: Automatic image optimization and lazy loading
- **Code Splitting**: Route-based and component-based code splitting
- **Bundle Analysis**: Webpack bundle analyzer for optimization
- **Caching**: Aggressive caching with React Query and Next.js
- **Compression**: Gzip compression for static assets

### SEO Implementation
```typescript
// Dynamic metadata generation
export async function generateMetadata({ params }): Promise<Metadata> {
  const product = await getProduct(params.slug);

  return {
    title: `${product.name} | Yateem Optician`,
    description: product.description,
    keywords: product.tags.join(', '),
    openGraph: {
      title: product.name,
      description: product.description,
      images: [product.thumbnail],
      type: 'product'
    }
  };
}
```

### Core Web Vitals
- **LCP (Largest Contentful Paint)**: Optimized with image preloading
- **FID (First Input Delay)**: Minimized with code splitting
- **CLS (Cumulative Layout Shift)**: Prevented with proper sizing
- **TTFB (Time to First Byte)**: Optimized with edge caching

## Deployment

### Recommended Platforms
- **Vercel**: Optimal for Next.js applications with automatic deployments
- **Netlify**: Alternative platform with good Next.js support
- **AWS Amplify**: Enterprise-grade hosting with AWS integration

### Environment Variables for Production
```env
# API Configuration
NEXT_PUBLIC_API_URL=https://api.yateem.com/api/web/
NEXT_PUBLIC_IMAGE_DOMAIN=api.yateem.com

# Analytics
GAID=G-XXXXXXXXXX
GTID=GTM-XXXXXXX

# Payment Gateways
TAMARA_PUBLIC_KEY=your_production_tamara_key

# Third-party Services
GOOGLE_MAPS_API_KEY=your_production_maps_key
LUXOTTICA_VM_KEY=your_production_vm_key
```

### Deployment Checklist
- [ ] All environment variables configured
- [ ] SSL certificate installed
- [ ] CDN configured for static assets
- [ ] Error tracking implemented
- [ ] Performance monitoring enabled
- [ ] SEO meta tags verified
- [ ] Analytics tracking confirmed
- [ ] Payment gateways tested
- [ ] API endpoints accessible
## Troubleshooting

### Common Issues

#### Build Errors
```bash
# Clear Next.js cache
rm -rf .next
npm run build

# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

#### API Connection Issues
- Verify `NEXT_PUBLIC_API_URL` environment variable
- Check network connectivity to API server
- Validate API endpoint responses
- Review CORS configuration

#### Authentication Problems
- Clear browser cookies and localStorage
- Check JWT token expiration
- Verify API authentication endpoints
- Review middleware configuration

#### Styling Issues
- Check SCSS compilation errors
- Verify Bootstrap imports
- Review CSS specificity conflicts
- Test RTL layout for Arabic content

#### Performance Issues
- Analyze bundle size with webpack-bundle-analyzer
- Optimize images and assets
- Review React Query cache configuration
- Check for memory leaks in components

### Debug Mode
```bash
# Enable debug logging
DEBUG=* npm run dev

# Check build analysis
npm run build
npm run analyze
```

### Error Monitoring
- **Sentry**: Error tracking and performance monitoring
- **LogRocket**: Session replay and debugging
- **Console Logs**: Browser developer tools
- **Network Tab**: API request/response debugging

## Testing

### Testing Strategy
- **Unit Tests**: Component and utility function testing
- **Integration Tests**: API integration and user flow testing
- **E2E Tests**: End-to-end user journey testing
- **Visual Tests**: UI component visual regression testing

### Recommended Testing Tools
```bash
# Install testing dependencies
npm install --save-dev jest @testing-library/react @testing-library/jest-dom

# Run tests
npm run test
npm run test:watch
npm run test:coverage
```

### Test Examples
```typescript
// Component test
import { render, screen } from '@testing-library/react';
import { ProductCard } from './ProductCard';

test('renders product card with correct information', () => {
  const product = {
    name: 'Test Glasses',
    price: 100,
    image: '/test-image.jpg'
  };

  render(<ProductCard product={product} />);

  expect(screen.getByText('Test Glasses')).toBeInTheDocument();
  expect(screen.getByText('100')).toBeInTheDocument();
});
```

## Security Considerations

### Data Protection
- **HTTPS**: All communications encrypted with SSL/TLS
- **JWT Tokens**: Secure token-based authentication
- **Cookie Security**: HttpOnly and Secure cookie flags
- **Input Validation**: Server-side and client-side validation
- **XSS Protection**: Content Security Policy headers

### API Security
- **Authentication**: JWT token validation
- **Authorization**: Role-based access control
- **Rate Limiting**: API request throttling
- **CORS**: Cross-origin resource sharing configuration
- **Input Sanitization**: Prevent injection attacks

### Privacy Compliance
- **GDPR**: European data protection compliance
- **Cookie Consent**: User consent for tracking cookies
- **Data Minimization**: Collect only necessary user data
- **Right to Deletion**: User data deletion capabilities

## Maintenance & Updates

### Regular Maintenance Tasks
- **Dependency Updates**: Keep packages up to date
- **Security Patches**: Apply security updates promptly
- **Performance Monitoring**: Track Core Web Vitals
- **Error Monitoring**: Review and fix reported errors
- **Content Updates**: Keep product catalogs current

### Update Procedures
```bash
# Check for outdated packages
npm outdated

# Update dependencies
npm update

# Update major versions (with caution)
npm install package@latest
```

### Monitoring & Analytics
- **Google Analytics**: User behavior and conversion tracking
- **Google Search Console**: SEO performance monitoring
- **Core Web Vitals**: Performance metrics tracking
- **Error Rates**: Application stability monitoring
- **API Performance**: Backend response time monitoring

---

## Conclusion

This documentation provides a comprehensive overview of the Yateem Optician Frontend application. The platform represents a modern, scalable e-commerce solution specifically designed for the optical industry with advanced features like virtual try-on, multi-store support, and comprehensive internationalization.

### Key Strengths
- **Modern Technology Stack**: Built with Next.js 14 and latest React features
- **Comprehensive Features**: Full e-commerce functionality with specialized optical features
- **Multi-Market Support**: Serves multiple Middle Eastern markets with localization
- **Performance Optimized**: Fast loading times and excellent user experience
- **Scalable Architecture**: Well-structured codebase for future growth

### Future Enhancements
- **Mobile App**: React Native or Flutter mobile application
- **AI Recommendations**: Machine learning-powered product recommendations
- **Advanced Analytics**: Enhanced user behavior tracking
- **Inventory Management**: Real-time inventory synchronization
- **Customer Service**: Integrated chat and support systems

For additional support or questions, please refer to the development team or create an issue in the project repository.

## FAQ / Troubleshooting
- **Q:** App fails to fetch API data?
  - **A:** Check `NEXT_PUBLIC_API_URL` and CORS settings on backend. Ensure your token is valid and the backend is reachable from your environment.
- **Q:** Styles not loading?
  - **A:** Ensure SCSS and Bootstrap imports are not commented out. Check for build errors related to SCSS modules.
- **Q:** Locale not switching?
  - **A:** Check context provider and translation files. Ensure the correct locale is being passed in headers.
- **Q:** Images not loading?
  - **A:** Check `NEXT_PUBLIC_IMAGE_DOMAIN` and verify image URLs are correct and accessible.
- **Q:** Build fails on deploy?
  - **A:** Ensure all required environment variables are set. Check build logs for missing dependencies or misconfigured scripts.
- **Q:** API returns 401/403?
  - **A:** User session may have expired. Try logging in again. If persistent, check token logic in the Axios interceptor.

## Developer Workflow
- **Branching:** Use feature branches (e.g., `feature/checkout-flow`) for new features. Use `bugfix/` or `hotfix/` prefixes for fixes.
- **Commits:** Write clear, descriptive commit messages. Use `--to build` in commit messages to trigger deployment.
- **Pull Requests:** Ensure your PR passes all CI checks and includes tests where relevant. Tag reviewers and provide context for your changes.
- **Code Style:** Follow the existing code style (TypeScript, SCSS modules, functional React components). Use Prettier and ESLint for formatting and linting.
- **Testing:** Add/maintain tests for new features and bug fixes. Use Jest and React Testing Library if present.

## Onboarding Checklist
- [ ] Clone the repository and install dependencies
- [ ] Create your `.env` file (see Setup section)
- [ ] Run the dev server and verify it works locally
- [ ] Test API connectivity (login, fetch products, etc.)
- [ ] Review code structure and key files (see Project Structure)
- [ ] Set up your code editor with Prettier and ESLint
- [ ] Run linter and fix any issues
- [ ] Run tests (if present)
- [ ] Read and follow the Developer Workflow section

## Workflow Diagram
Below is a simplified workflow for a typical feature development:

```mermaid
flowchart TD
    A[Create Feature Branch] --> B[Develop Feature]
    B --> C[Write/Update Tests]
    C --> D[Commit with --to build]
    D --> E[Open Pull Request]
    E --> F[CI/CD: Lint, Test, Build]
    F --> G[Code Review]
    G --> H[Merge to Main]
    H --> I[Deployment Triggered]
```

## Contributing
- Fork the repo and create a feature branch.
- Follow code style and naming conventions.
- Open a pull request with a clear description.
- Ensure all checks pass before requesting review.

## License
- [Specify license here, e.g., MIT, Apache 2.0, etc.]
- See `LICENSE` file for details.

## Appendices
- **Dependencies:** React, Next.js, Axios, React Query, Bootstrap, SCSS, next-intl, and more.
- **Common Commands:** See Available Scripts section above.
- **Contact:** For support, contact the maintainers or open an issue.

## Application Structure

- **`src/app`** 
  - Main entry point and contains routing for locales.

- **`src/components`** 
  - **Header**: Navbar and search functionalities.
  - **Footer**: Contact information and links.
  - **Home**: Various user-interactive components for the landing page.
  - **Authentication**: Context and components for login/logout.

- **`src/contexts`**: Contains context providers such as `AuthProvider` and `LocaleProvider`.

- **`src/lib/methods`**: API call methods, including authentication and user management.

## Deployment
- **Platform**: Deploy on Vercel or similar platforms supporting Next.js.

## Appendices
- **Dependencies**: Key libraries include React, Next.js, Axios, React Query, and various Bootstrap components.
- **Common Commands**: Additional scripts or common commands being utilized.
- **License Info**: To be detailed if required.

