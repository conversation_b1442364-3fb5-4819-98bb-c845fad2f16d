.filter {
  width: 30%;
  background-color: #fff;
  padding: 0rem 3.3rem 2rem 3.1rem;
  border-radius: 0 0 1.5rem 1.5rem;

  @media (max-width: 991.98px) {
    width: 100%;
    padding: 0rem 3.3rem 3rem 3.2rem;
    height: 100%;
    overflow-y: auto;
  }

  &_wrapper {
    .accordion {
      @media (max-width: 991.98px) {
        padding-bottom: 6rem;
      }

      &-body {
        padding: 0;
      }

      &-item {
        border: none;
        margin-top: 2.5rem;
        padding-bottom: 2rem;

        @media (max-width: 991.98px) {
          margin-top: 1.5rem;
        }

        &:not(:last-child) {
          border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }
      }

      &-collapse {
        margin-top: 1.3rem;
      }

      &-header {
        h5 {
          color: #000;
          font-size: 1.7rem;
          font-weight: 600;
          line-height: 2.1rem;
        }
      }

      &-button {
        padding: 0;

        &::after {
          filter: brightness(0) saturate(100%) invert(0%) sepia(94%) saturate(10%) hue-rotate(21deg) brightness(95%) contrast(100%);
          margin-right: 12px;
        }

        &:not(.collapsed) {
          background: none;
          box-shadow: none;
        }

        &:focus {
          background: none;
          box-shadow: none;
        }
      }
    }
  }

  &_grids {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.4rem;

    img {
      // width: 6.8113rem;
      width: 90%;
      // height: 3.3705rem;
      height: fit-content;
      object-fit: contain;
      max-height: 60px;
    }

    .brands-border {
      border-radius: 1.5rem;
      border: 1px solid #adadad;
      background: #fff;
      min-height: 8.2016rem;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      flex-direction: column;
      position: relative;
      cursor: pointer;
      width: 100%;
      overflow: hidden;

      &:has(input:checked) {
        border: 2.7px solid black;
      }

      &:hover {
        border: 1.5px solid #878787;
      }

      span {
        color: #000;
        font-size: 1.3rem;
        font-weight: 400;
        line-height: 1.6rem;
        text-align: center;
        max-width: 90%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      input {
        appearance: none;
        position: absolute;
        inset: 0;

        &:checked~label {
          background-color: red;
        }
      }
    }
  }

  &_inputs {
    display: flex;
    flex-direction: column;

    label {
      line-height: 1.8rem;
      display: flex;
      align-items: center;
      column-gap: 1rem;
      font-size: 1.5rem;
      color: #000;
      font-weight: 400;
      cursor: pointer;

      &:not(:last-child) {
        margin-bottom: 1.5rem;
      }
    }

    input[type="checkbox"] {
      width: 2rem;
      height: 2rem;
      border-radius: 0.4rem;
      border: 1px solid #adadad;
      background: #fff;
      //   appearance: none;
    }
  }

  .range-slider {
    margin-top: 2.7rem;
    height: 5px !important;

    &__thumb {
      background: #000 !important;
      width: 2.2rem !important;
      height: 2.2rem !important;
    }

    &__range {
      background: #000 !important;
    }

    &__flex {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 1.8rem;

      span {
        color: #000;
        font-size: 1.5rem;
        font-weight: 400;
        line-height: 1.89rem;
      }
    }
  }

  .view-more-button {
    background-color: transparent;
    padding: 0;
    border: none;
    color: #000;
    text-align: center;
    font-size: 1.4rem;
    font-weight: 400;
    line-height: 1.89rem;
    margin-top: 1.8rem;
    cursor: pointer;
    text-decoration: underline;
    text-align: left;
  }
}

.app.rtl {
  .filter {
    padding: 0rem 3.1rem 2rem 3.3rem;
  }
}